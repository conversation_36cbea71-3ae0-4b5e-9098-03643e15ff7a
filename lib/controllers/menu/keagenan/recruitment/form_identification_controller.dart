import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:best_ktp_ocr_flutter/bestktpocrflutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/utils/form_validation.dart';
import 'package:image/image.dart' as img;

class FormIdentificationController extends BaseControllers {
  final RecruitmentFormController baseController;
  FormIdentificationController({required this.baseController});

  List<String> monthList = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'Mei',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Okt',
    'Nov',
    'Des',
  ];

  // Gender
  // 0 -> <PERSON><PERSON>-laki
  // 1 -> Perempuan
  RxInt selectedJenisKelamin = 0.obs;

  // isAddressSame
  // 0 -> Sama
  // 1 -> Beda
  RxInt selectedIsAddressSame = 0.obs;

  RxString selectedMaritalStatus = 'KAWIN'.obs;
  List<String> maritalStatusList = [
    'KAWIN',
    'BELUM KAWIN',
    'CERAI',
    'CERAI MATI',
  ];

  // FormIdentification
  final nikController = TextEditingController();
  final namaKtpController = TextEditingController();
  final tempatLahirController = TextEditingController();
  final tanggalLahirController = TextEditingController();
  final bulanLahirController = TextEditingController();
  final tahunLahirController = TextEditingController();
  final jenisKelaminController = TextEditingController();
  final alamatKtpController = TextEditingController();
  final rtKtpController = TextEditingController();
  final rwKtpController = TextEditingController();
  final provinsiKtpController = TextEditingController();
  final kabupatenKtpController = TextEditingController();
  final kecamatanKtpController = TextEditingController();
  final kelurahanKtpController = TextEditingController();

  // Alamat Domisili (Home Address)
  final alamatDomisiliController = TextEditingController();
  final rtDomisiliController = TextEditingController();
  final rwDomisiliController = TextEditingController();
  final provinsiDomisiliController = TextEditingController();
  final kabupatenDomisiliController = TextEditingController();
  final kecamatanDomisiliController = TextEditingController();
  final kelurahanDomisiliController = TextEditingController();

  // Validation errors
  RxString nikError = ''.obs;
  RxString namaKtpError = ''.obs;
  RxString tempatLahirError = ''.obs;
  RxString tanggalLahirError = ''.obs;
  RxString bulanLahirError = ''.obs;
  RxString tahunLahirError = ''.obs;
  RxString alamatKtpError = ''.obs;
  RxString rtKtpError = ''.obs;
  RxString rwKtpError = ''.obs;
  RxString provinsiKtpError = ''.obs;
  RxString kabupatenKtpError = ''.obs;
  RxString kecamatanKtpError = ''.obs;
  RxString kelurahanKtpError = ''.obs;

  // Validation errors for domisili (only when different from KTP)
  RxString alamatDomisiliError = ''.obs;
  RxString rtDomisiliError = ''.obs;
  RxString rwDomisiliError = ''.obs;
  RxString provinsiDomisiliError = ''.obs;
  RxString kabupatenDomisiliError = ''.obs;
  RxString kecamatanDomisiliError = ''.obs;
  RxString kelurahanDomisiliError = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _setupFormChangeListeners();
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
  }

  // Setup listener untuk perubahan form
  void _setupFormChangeListeners() {
    // Tambahkan listener untuk FormIdentification
    nikController.addListener(_onFormChanged);
    namaKtpController.addListener(_onFormChanged);
    tempatLahirController.addListener(_onFormChanged);
    tanggalLahirController.addListener(_onFormChanged);
    bulanLahirController.addListener(_onFormChanged);
    tahunLahirController.addListener(_onFormChanged);
    jenisKelaminController.addListener(_onFormChanged);
    alamatKtpController.addListener(_onFormChanged);
    rtKtpController.addListener(_onFormChanged);
    rwKtpController.addListener(_onFormChanged);
    provinsiKtpController.addListener(_onFormChanged);
    kabupatenKtpController.addListener(_onFormChanged);
    kecamatanKtpController.addListener(_onFormChanged);
    kelurahanKtpController.addListener(_onFormChanged);

    // Tambahkan listener untuk Alamat Domisili
    alamatDomisiliController.addListener(_onFormChanged);
    rtDomisiliController.addListener(_onFormChanged);
    rwDomisiliController.addListener(_onFormChanged);
    provinsiDomisiliController.addListener(_onFormChanged);
    kabupatenDomisiliController.addListener(_onFormChanged);
    kecamatanDomisiliController.addListener(_onFormChanged);
    kelurahanDomisiliController.addListener(_onFormChanged);

    // Tambahkan listener untuk perubahan selection
    selectedJenisKelamin.listen((_) => _onFormChanged());
    selectedMaritalStatus.listen((_) => _onFormChanged());
    selectedIsAddressSame.listen((_) => _onFormChanged());
  }

  // Handler ketika form berubah
  void _onFormChanged() {
    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Method untuk copy alamat KTP ke alamat domisili
  void copyKtpAddressToDomisili() {
    if (selectedIsAddressSame.value == 0) {
      alamatDomisiliController.text = alamatKtpController.text;
      rtDomisiliController.text = rtKtpController.text;
      rwDomisiliController.text = rwKtpController.text;
      provinsiDomisiliController.text = provinsiKtpController.text;
      kabupatenDomisiliController.text = kabupatenKtpController.text;
      kecamatanDomisiliController.text = kecamatanKtpController.text;
      kelurahanDomisiliController.text = kelurahanKtpController.text;
    }
  }

  // Method untuk clear alamat domisili
  void clearDomisiliAddress() {
    if (selectedIsAddressSame.value == 1) {
      alamatDomisiliController.clear();
      rtDomisiliController.clear();
      rwDomisiliController.clear();
      provinsiDomisiliController.clear();
      kabupatenDomisiliController.clear();
      kecamatanDomisiliController.clear();
      kelurahanDomisiliController.clear();
    }
  }

  Future<void> processKtpOcr(File file) async {
    final preprocessedFile = await preprocessImage(file);

    var ktpJson = await Bestktpocrflutter().scanKTP(
      preprocessedFile.readAsBytesSync(),
    );

    print('here $ktpJson');
    var decodeJson = jsonDecode(ktpJson!);

    nikController.text = decodeJson['nik'] == 'null' ? '' : decodeJson['nik'];
    namaKtpController.text =
        decodeJson['nama'] == 'null' ? '' : decodeJson['nama'];
    tempatLahirController.text =
        decodeJson['tempatLahir'] == 'null' ? '' : decodeJson['tempatLahir'];
    tanggalLahirController.text =
        decodeJson['tanggalLahir'] == 'null'
            ? ''
            : decodeJson['tanggalLahir'].toString().split('-')[0];
    bulanLahirController.text =
        decodeJson['tanggalLahir'] == 'null'
            ? ''
            : getMonth(
              int.parse(decodeJson['tanggalLahir'].toString().split('-')[1]),
            );
    tahunLahirController.text =
        decodeJson['tanggalLahir'] == 'null'
            ? ''
            : decodeJson['tanggalLahir'].toString().split('-')[2];
    selectedJenisKelamin.value =
        decodeJson['jenisKelamin'].toString().toUpperCase() == 'LAKI-LAKI'
            ? 0
            : 1;
    selectedMaritalStatus.value =
        decodeJson['statusPerkawinan'] == 'null'
            ? 'KAWIN'
            : decodeJson['statusPerkawinan'];
    alamatKtpController.text =
        decodeJson['alamat'] == 'null' ? '' : decodeJson['alamat'];
    rtKtpController.text =
        decodeJson['rtrw'] == null ? '' : decodeJson['rtrw'].split('/')[0];
    rwKtpController.text =
        decodeJson['rtrw'] == null ? '' : decodeJson['rtrw'].split('/')[1];
    kelurahanKtpController.text =
        decodeJson['kelurahan'] == 'null' ? '' : decodeJson['kelurahan'];
    kecamatanKtpController.text =
        decodeJson['kecamatan'] == 'null' ? '' : decodeJson['kecamatan'];
    provinsiKtpController.text =
        decodeJson['provinsi'] == 'null' ? '' : decodeJson['provinsi'];
    kabupatenKtpController.text =
        decodeJson['kota'] == 'null' ? '' : decodeJson['kota'];
  }

  String getMonth(int month) {
    String res = 'Jan';
    res = monthList[month - 1];
    return res;
  }

  // Populate form data from model
  void populateFormData(RecruitmentFormModel formData) {
    // Isi FormIdentification dengan data
    nikController.text = formData.nik ?? '';
    namaKtpController.text = formData.namaKtp ?? '';
    tempatLahirController.text = formData.tempatLahir ?? '';
    tanggalLahirController.text = formData.tanggalLahir ?? '';
    bulanLahirController.text = formData.bulanLahir ?? '';
    tahunLahirController.text = formData.tahunLahir ?? '';
    jenisKelaminController.text = formData.jenisKelamin ?? '';
    selectedJenisKelamin.value = formData.jenisKelamin == 'Laki-laki' ? 0 : 1;
    selectedMaritalStatus.value =
        formData.maritalStatus == ''
            ? 'KAWIN'
            : formData.maritalStatus ?? 'KAWIN';
    alamatKtpController.text = formData.alamatKtp ?? '';
    rtKtpController.text = formData.rtKtp ?? '';
    rwKtpController.text = formData.rwKtp ?? '';
    provinsiKtpController.text = formData.provinsiKtp ?? '';
    kabupatenKtpController.text = formData.kabupatenKtp ?? '';
    kecamatanKtpController.text = formData.kecamatanKtp ?? '';
    kelurahanKtpController.text = formData.kelurahanKtp ?? '';

    // Isi Alamat Domisili dengan data
    alamatDomisiliController.text = formData.alamatDomisili ?? '';
    rtDomisiliController.text = formData.rtDomisili ?? '';
    rwDomisiliController.text = formData.rwDomisili ?? '';
    provinsiDomisiliController.text = formData.provinsiDomisili ?? '';
    kabupatenDomisiliController.text = formData.kabupatenDomisili ?? '';
    kecamatanDomisiliController.text = formData.kecamatanDomisili ?? '';
    kelurahanDomisiliController.text = formData.kelurahanDomisili ?? '';
  }

  // Validate form identification
  bool validateForm() {
    bool isValid = true;

    // Clear previous errors
    _clearAllErrors();

    // Validate KTP data
    final nikError = FormValidation.validateNIK(nikController.text);
    if (nikError != null) {
      this.nikError.value = nikError;
      isValid = false;
    }

    final namaError = FormValidation.validateRequired(
      namaKtpController.text,
      'Nama lengkap sesuai KTP',
    );
    if (namaError != null) {
      namaKtpError.value = namaError;
      isValid = false;
    }

    final tempatLahirError = FormValidation.validateRequired(
      tempatLahirController.text,
      'Tempat lahir',
    );
    if (tempatLahirError != null) {
      this.tempatLahirError.value = tempatLahirError;
      isValid = false;
    }

    final tanggalError = FormValidation.validateDate(
      tanggalLahirController.text,
    );
    if (tanggalError != null) {
      tanggalLahirError.value = tanggalError;
      isValid = false;
    }

    final bulanError = FormValidation.validateRequired(
      bulanLahirController.text,
      'Bulan lahir',
    );
    if (bulanError != null) {
      bulanLahirError.value = bulanError;
      isValid = false;
    }

    final tahunError = FormValidation.validateYear(tahunLahirController.text);
    if (tahunError != null) {
      tahunLahirError.value = tahunError;
      isValid = false;
    }

    // Validate KTP address
    final alamatKtpError = FormValidation.validateRequired(
      alamatKtpController.text,
      'Alamat KTP',
    );
    if (alamatKtpError != null) {
      this.alamatKtpError.value = alamatKtpError;
      isValid = false;
    }

    final rtKtpError = FormValidation.validateRtRw(
      rtKtpController.text,
      'RT KTP',
    );
    if (rtKtpError != null) {
      this.rtKtpError.value = rtKtpError;
      isValid = false;
    }

    final rwKtpError = FormValidation.validateRtRw(
      rwKtpController.text,
      'RW KTP',
    );
    if (rwKtpError != null) {
      this.rwKtpError.value = rwKtpError;
      isValid = false;
    }

    final provinsiKtpError = FormValidation.validateRequired(
      provinsiKtpController.text,
      'Provinsi KTP',
    );
    if (provinsiKtpError != null) {
      this.provinsiKtpError.value = provinsiKtpError;
      isValid = false;
    }

    final kabupatenKtpError = FormValidation.validateRequired(
      kabupatenKtpController.text,
      'Kabupaten/Kota KTP',
    );
    if (kabupatenKtpError != null) {
      this.kabupatenKtpError.value = kabupatenKtpError;
      isValid = false;
    }

    final kecamatanKtpError = FormValidation.validateRequired(
      kecamatanKtpController.text,
      'Kecamatan KTP',
    );
    if (kecamatanKtpError != null) {
      this.kecamatanKtpError.value = kecamatanKtpError;
      isValid = false;
    }

    final kelurahanKtpError = FormValidation.validateRequired(
      kelurahanKtpController.text,
      'Kelurahan/Desa KTP',
    );
    if (kelurahanKtpError != null) {
      this.kelurahanKtpError.value = kelurahanKtpError;
      isValid = false;
    }

    // Validate domisili address only if different from KTP
    if (selectedIsAddressSame.value == 1) {
      final alamatDomisiliError = FormValidation.validateRequired(
        alamatDomisiliController.text,
        'Alamat domisili',
      );
      if (alamatDomisiliError != null) {
        this.alamatDomisiliError.value = alamatDomisiliError;
        isValid = false;
      }

      final rtDomisiliError = FormValidation.validateRtRw(
        rtDomisiliController.text,
        'RT domisili',
      );
      if (rtDomisiliError != null) {
        this.rtDomisiliError.value = rtDomisiliError;
        isValid = false;
      }

      final rwDomisiliError = FormValidation.validateRtRw(
        rwDomisiliController.text,
        'RW domisili',
      );
      if (rwDomisiliError != null) {
        this.rwDomisiliError.value = rwDomisiliError;
        isValid = false;
      }

      final provinsiDomisiliError = FormValidation.validateRequired(
        provinsiDomisiliController.text,
        'Provinsi domisili',
      );
      if (provinsiDomisiliError != null) {
        this.provinsiDomisiliError.value = provinsiDomisiliError;
        isValid = false;
      }

      final kabupatenDomisiliError = FormValidation.validateRequired(
        kabupatenDomisiliController.text,
        'Kabupaten/Kota domisili',
      );
      if (kabupatenDomisiliError != null) {
        this.kabupatenDomisiliError.value = kabupatenDomisiliError;
        isValid = false;
      }

      final kecamatanDomisiliError = FormValidation.validateRequired(
        kecamatanDomisiliController.text,
        'Kecamatan domisili',
      );
      if (kecamatanDomisiliError != null) {
        this.kecamatanDomisiliError.value = kecamatanDomisiliError;
        isValid = false;
      }

      final kelurahanDomisiliError = FormValidation.validateRequired(
        kelurahanDomisiliController.text,
        'Kelurahan/Desa domisili',
      );
      if (kelurahanDomisiliError != null) {
        this.kelurahanDomisiliError.value = kelurahanDomisiliError;
        isValid = false;
      }
    }

    return isValid;
  }

  void _clearAllErrors() {
    nikError.value = '';
    namaKtpError.value = '';
    tempatLahirError.value = '';
    tanggalLahirError.value = '';
    bulanLahirError.value = '';
    tahunLahirError.value = '';
    alamatKtpError.value = '';
    rtKtpError.value = '';
    rwKtpError.value = '';
    provinsiKtpError.value = '';
    kabupatenKtpError.value = '';
    kecamatanKtpError.value = '';
    kelurahanKtpError.value = '';

    alamatDomisiliError.value = '';
    rtDomisiliError.value = '';
    rwDomisiliError.value = '';
    provinsiDomisiliError.value = '';
    kabupatenDomisiliError.value = '';
    kecamatanDomisiliError.value = '';
    kelurahanDomisiliError.value = '';
  }

  @override
  void onClose() {
    // Dispose all controllers
    nikController.dispose();
    namaKtpController.dispose();
    tempatLahirController.dispose();
    tanggalLahirController.dispose();
    bulanLahirController.dispose();
    tahunLahirController.dispose();
    jenisKelaminController.dispose();
    alamatKtpController.dispose();
    rtKtpController.dispose();
    rwKtpController.dispose();
    provinsiKtpController.dispose();
    kabupatenKtpController.dispose();
    kecamatanKtpController.dispose();
    kelurahanKtpController.dispose();

    // Alamat Domisili controllers
    alamatDomisiliController.dispose();
    rtDomisiliController.dispose();
    rwDomisiliController.dispose();
    provinsiDomisiliController.dispose();
    kabupatenDomisiliController.dispose();
    kecamatanDomisiliController.dispose();
    kelurahanDomisiliController.dispose();

    super.onClose();
  }
}

Future<File> preprocessImage(File file) async {
  final bytes = await file.readAsBytes();
  img.Image? image = img.decodeImage(bytes);
  if (image == null) return file;

  // 1. Grayscale
  image = img.grayscale(image);

  // 2. Adjust contrast
  image = img.adjustColor(image, contrast: 1.5);

  // 4. Resize jika perlu
  image = img.copyResize(image, width: 1200);

  final processedBytes = img.encodeJpg(image, quality: 100);

  final output = File('${file.parent.path}/preprocessed.jpg');
  await output.writeAsBytes(processedBytes);

  return output;
}
