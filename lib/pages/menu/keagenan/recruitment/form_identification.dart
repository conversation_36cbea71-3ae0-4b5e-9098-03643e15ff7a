import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pd_radio_button.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_identification_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

class FormIdentification extends StatelessWidget {
  final FormIdentificationController controller;

  const FormIdentification({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _ktpSection(context),
        SizedBox(height: paddingMedium),
        _addressKtpSection(context),
        SizedBox(height: paddingMedium),
        TitleWidget(title: 'Alamat Tempat Tinggal Saat Ini'),
        Container(
          padding: EdgeInsets.symmetric(vertical: paddingSmall),
          child: Obx(
            () => Column(
              children: [
                PdlRadioButton(
                  index: 0,
                  selectedIndex: controller.selectedIsAddressSame.value,
                  onTap: () {
                    controller.selectedIsAddressSame.value = 0;
                  },
                  title: 'Sama dengan KTP',
                ),
                SizedBox(height: paddingSmall),
                PdlRadioButton(
                  index: 1,
                  selectedIndex: controller.selectedIsAddressSame.value,
                  onTap: () {
                    controller.selectedIsAddressSame.value = 1;
                  },
                  title: 'Beda dengan KTP',
                ),
              ],
            ),
          ),
        ),
        Obx(() {
          if (controller.selectedIsAddressSame.value == 0) {
            return SizedBox();
          } else {
            return _addressKtpSection(context, isHomeAddress: true);
          }
        }),
      ],
    );
  }

  Column _addressKtpSection(BuildContext context, {bool? isHomeAddress}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (isHomeAddress != true) TitleWidget(title: 'Alamat Sesuai KTP'),
        Text(
          isHomeAddress == true
              ? 'Masukan alamat tempat tinggal saat ini'
              : 'pastikan data berikut sama persis dengan KTP.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            height: 2,
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Provinsi',
            textController:
                isHomeAddress == true
                    ? controller.provinsiDomisiliController
                    : controller.provinsiKtpController,
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Kabupaten / Kota',
            textController:
                isHomeAddress == true
                    ? controller.kabupatenDomisiliController
                    : controller.kabupatenKtpController,
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Kelurahan / Desa',
            textController:
                isHomeAddress == true
                    ? controller.kelurahanDomisiliController
                    : controller.kelurahanKtpController,
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Kecamatan',
            textController:
                isHomeAddress == true
                    ? controller.kecamatanDomisiliController
                    : controller.kecamatanKtpController,
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Alamat',
            textController:
                isHomeAddress == true
                    ? controller.alamatDomisiliController
                    : controller.alamatKtpController,
          ),
        ),
        _padding(
          Row(
            children: [
              Expanded(
                child: PdlTextField(
                  label: 'RT',
                  textController:
                      isHomeAddress == true
                          ? controller.rtDomisiliController
                          : controller.rtKtpController,
                ),
              ),
              SizedBox(width: paddingMedium),
              Expanded(
                child: PdlTextField(
                  label: 'RW',
                  textController:
                      isHomeAddress == true
                          ? controller.rwDomisiliController
                          : controller.rwKtpController,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Column _ktpSection(BuildContext context) {
    print(controller.selectedMaritalStatus.value);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleWidget(title: 'Data Diri Sesuai KTP'),
        Text(
          'Mohon mengisi data berikut sama persis dengan KTP.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            height: 2,
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'NIK',
              textController: controller.nikController,
              hasError: controller.nikError.value.isNotEmpty,
              errorText:
                  controller.nikError.value.isEmpty
                      ? null
                      : controller.nikError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Nama Lengkap Sesuai KTP',
              textController: controller.namaKtpController,
              hasError: controller.namaKtpError.value.isNotEmpty,
              errorText:
                  controller.namaKtpError.value.isEmpty
                      ? null
                      : controller.namaKtpError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Tempat Lahir',
              textController: controller.tempatLahirController,
              hasError: controller.tempatLahirError.value.isNotEmpty,
              errorText:
                  controller.tempatLahirError.value.isEmpty
                      ? null
                      : controller.tempatLahirError.value,
            ),
          ),
        ),
        _padding(
          Row(
            children: [
              Expanded(
                child: Obx(
                  () => PdlTextField(
                    label: 'Tanggal',
                    textController: controller.tanggalLahirController,
                    hasError: controller.tanggalLahirError.value.isNotEmpty,
                    errorText:
                        controller.tanggalLahirError.value.isEmpty
                            ? null
                            : controller.tanggalLahirError.value,
                  ),
                ),
              ),
              SizedBox(width: paddingMedium),
              Expanded(
                child: Obx(
                  () => PdlDropDown(
                    item: controller.monthList,
                    selectedItem:
                        controller.bulanLahirController.text.isEmpty
                            ? 'Jan'
                            : controller.bulanLahirController.text,
                    title: 'Bulan',
                    disableSearch: true,
                    onChanged:
                        (val) =>
                            controller.bulanLahirController.text = val ?? '',
                    enabled: true,
                    hasError: controller.bulanLahirError.value.isNotEmpty,
                    errorText:
                        controller.bulanLahirError.value.isEmpty
                            ? null
                            : controller.bulanLahirError.value,
                  ),
                ),
              ),
              SizedBox(width: paddingMedium),
              Expanded(
                child: Obx(
                  () => PdlTextField(
                    label: 'Tahun',
                    textController: controller.tahunLahirController,
                    hasError: controller.tahunLahirError.value.isNotEmpty,
                    errorText:
                        controller.tahunLahirError.value.isEmpty
                            ? null
                            : controller.tahunLahirError.value,
                  ),
                ),
              ),
            ],
          ),
        ),
        _padding(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Jenis Kelamin',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              SizedBox(height: paddingSmall),
              Obx(
                () => Wrap(
                  spacing: paddingMedium,
                  children: [
                    PdlRadioButton(
                      index: 0,
                      selectedIndex: controller.selectedJenisKelamin.value,
                      title: 'Laki-laki',
                      onTap: () {
                        controller.selectedJenisKelamin.value = 0;
                        controller.jenisKelaminController.text = 'Laki-laki';
                      },
                    ),
                    PdlRadioButton(
                      index: 1,
                      selectedIndex: controller.selectedJenisKelamin.value,
                      title: 'Perempuan',
                      onTap: () {
                        controller.selectedJenisKelamin.value = 1;
                        controller.jenisKelaminController.text = 'Perempuan';
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        _padding(
          PdlDropDown(
            item: controller.maritalStatusList,
            selectedItem:
                controller.selectedMaritalStatus.value == ''
                    ? 'KAWIN'
                    : controller.selectedMaritalStatus.value ?? 'KAWIN',
            title: 'Status Perkawinan',
            onChanged: (val) {
              if (val != null) {
                controller.selectedMaritalStatus.value = val;
                controller.maritalStatusController.text = val;
              }
            },
            disableSearch: true,
            enabled: true,
          ),
        ),
      ],
    );
  }

  Padding _padding(Widget child) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: child,
    );
  }
}
